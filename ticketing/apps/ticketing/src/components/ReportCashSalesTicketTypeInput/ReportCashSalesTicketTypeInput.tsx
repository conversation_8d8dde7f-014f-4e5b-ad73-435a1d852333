import { useEffect, useMemo, useState } from 'react';

import { Currency, PriceInput } from 'ticketing-shared';

import { Input } from '@hudl/uniform-web-forms-legacy';
import { formatMessage } from 'frontends-i18n';

import { TicketedEvent, TicketType } from '../../graphql/generated/graphqlTypes';

import styles from './ReportCashSalesTicketTypeInput.module.scss';

type Props = {
  ticketType: TicketType;
  ticketedEvent: TicketedEvent;
  onQuantityChange: (ticketTypeId: string, quantity: number) => void;
};

function ReportCashSalesTicketTypeInput({ ticketType, onQuantityChange }: Props): React.JSX.Element {
  const [quantity, setQuantity] = useState<number>(0);

  useEffect(() => {
    onQuantityChange(ticketType.id, quantity);
  }, [onQuantityChange, quantity, ticketType.id]);

  const ticketTypeDisplay = useMemo(() => {
    return (
      <Input
        type="text"
        className={styles.flexInput}
        qaId="ticket-type-name-input"
        value={ticketType.name ?? ''}
        hasError={false}
        helpText={''}
        label={formatMessage({ id: 'ticketing.add-ticketing-page.quantity-for-sale' })}
        placeholder={formatMessage({ id: 'ticketing.add-ticketing-page.quantity-placeholder' })}
        isReadOnly={true}
      />
    );
  }, [ticketType]);

  const priceDisplay = useMemo(() => {
    return (
      <PriceInput
        currency={Currency.USD}
        priceInCents={ticketType.priceInCents ?? 0}
        onPriceChange={() => {}}
        isReadOnly
        label={ticketType.name ?? ''}
      />
    );
  }, [ticketType]);

  const quantityDisplay = useMemo(() => {
    return (
      <Input
        type="number"
        className={styles.flexInput}
        qaId="report-cash-sales-quantity-input"
        value={quantity}
        hasError={false}
        helpText={''}
        label={formatMessage({ id: 'ticketing.add-ticketing-page.quantity-for-sale' })}
        onChange={(e) => {
          setQuantity(Number(e.target.value));
        }}
        placeholder={formatMessage({ id: 'ticketing.add-ticketing-page.quantity-placeholder' })}
        isReadOnly={false}
      />
    );
  }, [quantity]);

  const salesTotalDisplay = useMemo(() => {
    return (
      <PriceInput
        currency={Currency.USD}
        priceInCents={(ticketType.priceInCents ?? 0) * quantity}
        onPriceChange={() => {}}
        isReadOnly
        label={ticketType.name ?? ''}
      />
    );
  }, [ticketType, quantity]);

  return (
    <div data-qa-id="report-cash-sales-ticket-type-input" className={styles.reportCashSalesInputContainer}>
      <div data-qa-id="report-cash-sales-ticket-type-name-container">{ticketTypeDisplay}</div>
      <div data-qa-id="report-cash-sales-ticket-type-price-container">{priceDisplay}</div>
      <div data-qa-id="report-cash-sales-ticket-type-quantity-container">{quantityDisplay}</div>
      <div data-qa-id="report-cash-sales-ticket-type-total-container">{salesTotalDisplay}</div>
    </div>
  );
}

export default ReportCashSalesTicketTypeInput;
