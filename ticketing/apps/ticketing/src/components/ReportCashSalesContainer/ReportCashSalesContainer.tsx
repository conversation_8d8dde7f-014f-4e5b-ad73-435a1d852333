import { useCallback, useEffect, useMemo } from 'react';

import { useNavigate, useParams } from 'react-router-dom';

import { Logger } from '@hudl/frontends-logging';
import { Button, Headline, IconUiNavigationBack, Note, Spinner, Text, ToastMessenger } from '@hudl/uniform-web';
import { formatMessage } from 'frontends-i18n';

import { LoggingAttributes } from '../../enums/loggingAttributes';
import { TicketType } from '../../graphql/generated/graphqlTypes';
import useTicketedEventById from '../../graphql/hooks/useGetTicketedEventById';
import { loggingParams } from '../../utility/constants';
import { buildTicketedEventDetailsLink } from '../../utility/urlUtils';
import { reloadPage } from '../../utility/windowUtils';
import ReportCashSalesTicketTypeInput from '../ReportCashSalesTicketTypeInput/ReportCashSalesTicketTypeInput';

import styles from './ReportCashSalesContainer.module.scss';

function ReportCashSalesContainer(): React.JSX.Element {
  const { organizationId, ticketedEventId } = useParams();
  const navigate = useNavigate();
  const logger = useMemo(() => new Logger('Ticketing'), []);
  const { ticketedEvent, ticketedEventLoading, ticketedEventError } = useTicketedEventById(ticketedEventId!, {
    fetchPolicy: 'network-only',
  });

  const navigateBack = useCallback(() => {
    navigate(buildTicketedEventDetailsLink(organizationId!, ticketedEventId!));
  }, [organizationId, ticketedEventId, navigate]);

  const header = useMemo(() => {
    return (
      <div className={styles.headerContainer}>
        <div className={styles.headerNameContainer}>
          <Button
            buttonType="subtle"
            qaId="report-case-sales-back-button"
            icon={<IconUiNavigationBack />}
            onPress={navigateBack}
          />
          <Headline level="1" qaId="report-cash-sales-title" className={styles.headerTitle}>
            {formatMessage({ id: 'ticketing.report-cash-sales.title' })}
          </Headline>
        </div>
      </div>
    );
  }, [navigateBack]);

  const helperText = useMemo(() => {
    return (
      <Text qaId="report-cash-sales-helper-text" className={styles.helperText}>
        {formatMessage({ id: 'ticketing.report-cash-sales.helper-text' })}
      </Text>
    );
  }, []);

  const onQuantityChange = useCallback((ticketTypeId: string, quantity: number) => {
    console.log(ticketTypeId, quantity);
  }, []);

  const mapTicketType = useCallback(
    (ticketType: TicketType) => {
      return (
        <ReportCashSalesTicketTypeInput
          key={ticketType.id}
          ticketType={ticketType}
          ticketedEvent={ticketedEvent!}
          onQuantityChange={onQuantityChange}
        />
      );
    },
    [ticketedEvent, onQuantityChange]
  );

  const ticketTypes = useMemo(() => {
    return (
      <div data-qa-id="ticket-type-inputs-container" className={styles.ticketTypeInputsContainer}>
        {ticketedEvent?.ticketTypes?.map(mapTicketType)}
      </div>
    );
  }, [mapTicketType, ticketedEvent]);

  const loader = useMemo(() => {
    return (
      <div className={styles.errorAndLoadingContainer}>
        <Spinner size="large" qaId="event-details-page-spinner" />
      </div>
    );
  }, []);

  const loadingError = useMemo(() => {
    return (
      <div className={styles.errorAndLoadingContainer} data-qa-id="event-details-error">
        <Note type="critical" size="large" className={styles.errorNote}>
          {formatMessage({ id: 'ticketing.ticketed-event-details.error.note' })}
        </Note>
        <Button buttonType="subtle" onPress={reloadPage} qaId="event-details-page-reload-button">
          {formatMessage({ id: 'ticketing.reload' })}
        </Button>
      </div>
    );
  }, []);

  useEffect(() => {
    ToastMessenger.hide();
  }, []);

  useEffect(() => {
    logger.log('Viewed Report Cash Sales', {
      [LoggingAttributes.FUNC_ATTRIBUTE]: loggingParams.func.view,
      [LoggingAttributes.OP_ATTRIBUTE]: loggingParams.op.reportCashSales,
      [LoggingAttributes.PAGE_ATTRIBUTE]: loggingParams.page.reportCashSalesPage,
      [LoggingAttributes.TICKETED_EVENT_ID]: ticketedEventId,
      [LoggingAttributes.USER_AGENT_ATTRIBUTE]: navigator.userAgent || 'undefined',
    });
  }, [logger, ticketedEventId]);

  if (ticketedEventLoading) return <>{loader}</>;
  if (ticketedEventError) return <>{loadingError}</>;

  return (
    <div className={styles.reportCashSalesContainer} data-qa-id="report-cash-sales-container">
      <div className={styles.reportCashSalesContent}>
        <div data-qa-id="report-cash-sales-header-container">{header}</div>
        <div data-qa-id="report-cash-sales-helper-text-container">{helperText}</div>
        <div data-qa-id="report-cash-sales-ticket-types-container">{ticketTypes}</div>
      </div>
    </div>
  );
}

export default ReportCashSalesContainer;
